import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, combineLatest, EMPTY, forkJoin, Observable } from 'rxjs';
import { catchError, finalize, first, map, skipWhile, switchMap, take } from 'rxjs/operators';
import { SocialService } from '../../../composer/post';
import { ConfigService } from '../../../core/config/config.service';
import { ComposerStoreService } from '../../../composer/composer-store.service';
import { FileType, UploadedFile } from '../../../composer/interfaces';
import { Payload } from '../../../composer/shared/utils/payload';
import { CalendarView } from './interface';
import { SocialPost, SocialServiceType } from '../../../core/post/post';
import { Draft } from '../../../core/post/draft';
import { SocialPostFeedStoreService } from '../../../core/post/post-feed.service';
import { SMComposerApiService } from '../../../composer/composer-api.service';
import { ListArgs, SocialPostsAPIService } from '../../../core/post/social-posts.api.service';
import { SocialDraftsAPIService } from '../../../core/post/social-drafts.api.service';
import { FeedData, FeedItem } from '../../../core/post/interface';
import { Customization } from '../../../core/post/customization';
import { PostableService, SocialServiceService } from '../../../shared/social-service/social-service.service';
import { GmbCtaOptions, GmbEventOptions, GmbOptions } from '../../../composer/models/gmb-options';
import { WorkflowType } from '../../../composer/post';
import { CalendarEvent } from 'angular-calendar';

//  Importing and registering all extra locales in use
// Refer to this changelog:
// https://github.com/mattlewis92/angular-calendar/blob/74f74b75b61d420215efde04777b9f8ddcb05922/CHANGELOG.md#breaking-changes-1
import { registerLocaleData } from '@angular/common';
import localeFr from '@angular/common/locales/fr'; // to register french
import localeIt from '@angular/common/locales/it';
import localeDe from '@angular/common/locales/de';
import localeCs from '@angular/common/locales/cs';
import localeNl from '@angular/common/locales/nl';
import localeEs from '@angular/common/locales/es-419';
import { BlogStoreService } from '../blogs/blog-store.service';
import { DraftsService, DraftType, SSIDDraftTypeInterface } from '@vendasta/social-drafts';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { TranslateService } from '@ngx-translate/core';
import { MediaEntryInterface } from '@vendasta/composer';

registerLocaleData(localeFr);
registerLocaleData(localeIt);
registerLocaleData(localeDe);
registerLocaleData(localeCs);
registerLocaleData(localeEs);
registerLocaleData(localeNl);

const FETCH_DRAFTS_PAGE_SIZE = 200;

enum PostIdType {
  ParentTask,
  SubTask,
  SinglePost,
}

@Injectable()
export class PostsCalendarService {
  get currentView$(): Observable<CalendarView> {
    return this.currentView$$.asObservable();
  }

  constructor(
    private http: HttpClient,
    private config: ConfigService,
    private composer: ComposerStoreService,
    private composerApi: SMComposerApiService,
    private socialPostFeedService: SocialPostFeedStoreService,
    private socialPostsAPIService: SocialPostsAPIService,
    private socialServiceService: SocialServiceService,
    private socialDraftsAPIService: SocialDraftsAPIService,
    private draftsService: DraftsService,
    private blogStoreService: BlogStoreService,
    private snackService: SnackbarService,
    private translateService: TranslateService,
  ) {
    this.startDate = this.defaultStartDate();
    this.endDate = this.defaultEndDate();
  }

  get posts$(): Observable<SocialPost[]> {
    return this.feedItems$$.asObservable();
  }

  get isFeedLoading$(): Observable<boolean> {
    return this.isFeedLoading$$ as Observable<boolean>;
  }

  private currentView$$: BehaviorSubject<CalendarView> = new BehaviorSubject<CalendarView>(CalendarView.MONTH);

  startDate: Date;
  endDate: Date;
  feedItems$$: BehaviorSubject<SocialPost[]> = new BehaviorSubject<SocialPost[]>([]);
  cursor: string;

  isFeedLoading$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);

  private static getPostIdType(postId: string): PostIdType {
    const firstIndex = postId.indexOf(':');
    const lastIndex = postId.lastIndexOf(':');

    if (firstIndex === lastIndex) {
      return firstIndex === -1 ? PostIdType.SinglePost : PostIdType.ParentTask;
    }

    return PostIdType.SubTask;
  }

  setCurrentView(view: CalendarView): void {
    this.currentView$$.next(view);
  }

  defaultStartDate(): Date {
    const now = new Date();
    return new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0, 0);
  }

  defaultEndDate(): Date {
    const now = new Date();
    return new Date(now.getFullYear(), now.getMonth() + 1, 1, 0, 0, 0, 0);
  }

  fetchFeedItems(startDate?: Date, endDate?: Date): Observable<any> {
    this.startDate = startDate || this.defaultStartDate();
    this.endDate = endDate || this.defaultEndDate();
    this.feedItems$$.next([]);
    return this.fetchPosts();
  }

  fetchDraftItems(startDate?: Date, endDate?: Date): Observable<any> {
    this.startDate = startDate || this.defaultStartDate();
    this.endDate = endDate || this.defaultEndDate();
    return this.fetchDrafts();
  }

  fetchPosts(cursor?: string): Observable<any> {
    this.isFeedLoading$$.next(true);
    return combineLatest([
      this.socialServiceService.availablePostableServices$.pipe(
        first(),
        skipWhile((services) => !services),
        switchMap((services: PostableService[]) => {
          const args = this.buildFeedPostCallBody(services);
          return this.socialPostsAPIService.fetchPostData(args, cursor);
        }),
      ),
    ]).pipe(
      finalize(() => this.isFeedLoading$$.next(false)),
      map(([response]: [FeedData]) => {
        return response;
      }),
      switchMap((response: FeedData) => {
        return this.setFeedItems(response.feed_data as SocialPost[], response.cursor);
      }),
    );
  }

  fetchDrafts(cursor?: string): Observable<any> {
    return this.config.config$.pipe(
      switchMap((config) => {
        return this.socialDraftsAPIService
          .fetchDrafts({
            cursor: cursor,
            startDate: this.startDate,
            endDate: this.endDate,
            pageSize: FETCH_DRAFTS_PAGE_SIZE,
            showVisibility: config?.is_digital_agent ? 'all' : 'visible',
          })
          .pipe(
            finalize(() => this.isFeedLoading$$.next(false)),
            map((response: FeedData) => {
              const posts = this.convertDraftsToSocialPosts(response.feed_data as Draft[]);
              return this.setFeedItems(posts, response.cursor, true);
            }),
          );
      }),
    );
  }

  convertDraftsToSocialPosts(drafts: Draft[]): SocialPost[] {
    const socialPosts: SocialPost[] = [];
    drafts.forEach((draft) => {
      if (draft.isScheduled) {
        if (draft.services?.length === 0) {
          socialPosts.push(SocialPost.fromDraft(draft));
        } else {
          draft.service_data?.forEach((sd) => socialPosts.push(SocialPost.fromDraft(draft, null, sd)));
        }
      }
    });
    return socialPosts;
  }

  private buildFeedPostCallBody(services: PostableService[]): ListArgs {
    const ssids = services
      .filter(
        (service: PostableService) =>
          service.serviceType !== SocialService.GMB && service.serviceType !== SocialService.INSTAGRAM,
      )
      .map((service: PostableService) => service.ssid);
    const gmbProfileUrls = services
      .filter((service: PostableService) => service.serviceType === SocialService.GMB)
      .map((service: PostableService) => service.profileUrl);
    const googleMyBusinessPathName = gmbProfileUrls.length > 0 ? gmbProfileUrls[0] : null;
    const instagramUserIds = services
      .filter((service: PostableService) => service.serviceType === SocialService.INSTAGRAM)
      .map((service: PostableService) => service.ssid);
    const instagramUserId = instagramUserIds.length > 0 ? instagramUserIds[0] : null;
    const googleUserIds = services
      .filter((service: PostableService) => service.serviceType === SocialService.GMB)
      .map((service: PostableService) => service.googleUserId);
    const googleUserId = googleUserIds.length > 0 ? googleUserIds[0] : null;
    if (instagramUserId) {
      ssids.push(instagramUserId);
    }
    if (googleUserId && googleMyBusinessPathName) {
      ssids.push(`${googleUserId}:${googleMyBusinessPathName}`);
    }
    return {
      startDate: this.startDate,
      endDate: this.endDate,
      ssids: ssids,
      tags: [],
    };
  }

  private setFeedItems(posts: SocialPost[], cursor?: string, settingDrafts?: boolean): Observable<any> {
    const feed = this.feedItems$$.getValue();
    if (feed.length === 0) {
      this.feedItems$$.next(posts);
    } else {
      const unique = posts.filter((item) => {
        for (const f of feed) {
          if (
            (f.isDraft && f.draftId === item.draftId && f.itemId === item.itemId) ||
            f.postId === item.postId ||
            f.postId === item['socialPostId'] ||
            (f.postId === item.postId && f.scheduledDateTime != item.scheduledDateTime)
          ) {
            return false;
          }
        }
        return true;
      });
      this.feedItems$$.next(feed.concat(unique));
    }

    this.isFeedLoading$$.next(false);
    if (cursor) {
      return settingDrafts ? this.fetchDrafts(cursor) : this.fetchPosts(cursor);
    }
    return EMPTY;
  }

  updatePost(updatedPostIds: string[]): Observable<any> {
    this.isFeedLoading$$.next(true);
    const calls = updatedPostIds.map((postId) => {
      const url = `/angular/${this.config.accountGroupId}/fetch-post/?postId=${postId}&agid=${this.config.accountGroupId}`;
      return this.http.get(url).pipe(
        finalize(() => this.isFeedLoading$$.next(false)),
        map((response: { message: any }) => {
          const feedData = this.feedItems$$.getValue();
          const responseData = SocialPost.fromFetchPostResponse(response.message);
          const oldIndex = feedData.findIndex((f) => f.postId === responseData.postId);
          feedData.splice(oldIndex, 1, responseData);
          this.setFeedItems(feedData);
          return responseData;
        }),
      );
    });

    return forkJoin(calls);
  }

  removeItemFromUI(itemsToBeRemoved: string[]): any[] {
    const feedItems = this.feedItems$$.getValue();
    const filteredItems = feedItems.filter(
      (item) => !itemsToBeRemoved.includes(item.postId) && !itemsToBeRemoved.includes(item.draftId),
    );
    this.feedItems$$.next(filteredItems);
    return filteredItems;
  }

  updateDraftInUI(updatedDraft: Draft): void {
    const modifiedDrafts: SocialPost[] = [];
    let allDrafts = this.feedItems$$.getValue() || [];
    const foundDrafts = allDrafts.filter((d) => d.draftId === updatedDraft.draft_id) || [];
    if (updatedDraft.service_data.length > 0) {
      updatedDraft.service_data?.forEach((sd) => {
        const draft = foundDrafts.find((d) => d.draftId === updatedDraft.draft_id && d.ssid === sd.serviceId);
        modifiedDrafts.push(SocialPost.fromDraft(updatedDraft, draft?.itemId, sd));
      });
    } else {
      const draft = foundDrafts.find((d) => d.draftId === updatedDraft.draft_id);
      modifiedDrafts.push(SocialPost.fromDraft(updatedDraft, draft?.itemId));
    }
    const unmodifiedDrafts = allDrafts.filter((ad) => ad.draftId !== updatedDraft.draft_id);
    allDrafts = [...unmodifiedDrafts, ...modifiedDrafts];
    this.feedItems$$.next(allDrafts);
  }

  editPostDate(events: any, newDate: Date): Observable<any> {
    this.isFeedLoading$$.next(true);
    return this.posts$.pipe(
      take(1),
      finalize(() => this.isFeedLoading$$.next(false)),
      map((posts: SocialPost[]) => {
        const postsToEdit = [];
        events.forEach((e) => {
          const { isDraft: eIsDraft, postId: ePostId, draftId: eDraftId, itemId: eItemId } = e.event.meta;
          const found = eIsDraft
            ? posts.find((post: SocialPost) => post.draftId === eDraftId && post.itemId === eItemId)
            : posts.find((post: SocialPost) => post.postId === ePostId);
          if (!found) {
            throw new Error('Social Post not found, will not update');
          }
          found.lastUpdateDateTime = new Date();
          found.scheduledDateTime = newDate;
          postsToEdit.push(found);
        });
        return postsToEdit;
      }),
      switchMap((posts: SocialPost[]) => {
        if (posts[0]?.blogPostCustomization?.siteType.toLowerCase() === SocialServiceType.WORDPRESS) {
          this.editBlogPost(posts, newDate);
          return EMPTY;
        }

        this.preparePostSubmission(posts, newDate);
        if (posts[0].isDraft) {
          this.composer.submitDraft();
          return EMPTY;
        } else {
          const today = new Date();
          if (newDate > today) {
            return this.composer.editPosts(this.config.accountGroupId);
          }
          return this.composer.submitPost();
        }
      }),
      catchError(() => {
        return EMPTY;
      }),
    );
  }

  //By this we can handle the drag posts both draft and scheduled
  editBlogPost(posts: SocialPost[], newDate: Date) {
    const post = posts[0];
    const selectedSsids = post.ssid ? [post.ssid] : [];

    if (post.isDraft) {
      const ssidDraftMap: SSIDDraftTypeInterface[] =
        selectedSsids.length > 0
          ? [
              {
                ssid: selectedSsids[0],
                draftType: DraftType.DRAFT_TYPE_INVALID,
              },
            ]
          : [];

      //Todo Need to handle the composer microservice for update the image path
      const newMedia =
        posts[0]?.mediaEntries?.map((draft: MediaEntryInterface) => ({
          imageUrl: draft.mediaUrl,
        })) || [];
      const isHiddenDraft = post.isDraft;
      const blogPostCustomization = post.blogPostCustomization;

      this.draftsService
        .updateDraft(
          this.config.accountGroupId,
          post.draftId,
          post.postText,
          newDate,
          null,
          '',
          [],
          newMedia,
          selectedSsids,
          null,
          post.metaData,
          null,
          ssidDraftMap,
          isHiddenDraft,
          null,
          blogPostCustomization,
        )
        .subscribe({
          next: () => {
            this.snackService.openSuccessSnack(this.translateService.instant('SNACKBAR.DRAFT_UPDATED'));
          },
          error: () => {
            this.snackService.openErrorSnack(this.translateService.instant('BLOG.DRAFT_FAIL'));
          },
        });
    } else {
      //Todo Need to handle the composer microservice for update the image path
      const images =
        posts[0].mediaEntries?.map((entry: MediaEntryInterface) => ({
          publicUrl: entry.mediaUrl,
        })) || [];
      const request = {
        blogPost: {
          internalPostId: post.postId,
          socialServiceId: post.ssid,
          businessId: this.config.accountGroupId,
          title: post.title,
          content: post.postText,
          siteType: post.blogPostCustomization?.siteType,
          author: post.blogPostCustomization?.author,
          categories: post.blogPostCustomization?.categories,
          tags: post.tags,
          postDateTime: newDate,
          images: images,
        },
      };
      this.blogStoreService.updateScheduledBlog(request).subscribe({
        next: () => {
          this.snackService.openSuccessSnack(this.translateService.instant('BLOG.UPDATE_SCHEDULED'));
        },
        error: () => {
          this.snackService.openErrorSnack(this.translateService.instant('BLOG.SCHEDULE_FAIL'));
        },
      });
    }
  }

  // handles scenario: deleting one post from a group in Calendar day view
  deleteDraft(draftId: string, itemId): Observable<any> {
    this.isFeedLoading$$.next(true);
    return this.posts$.pipe(
      take(1),
      finalize(() => this.isFeedLoading$$.next(false)),
      map((posts: SocialPost[]) => {
        let postsToEdit = [];
        const found = posts.filter((p) => p.draftId === draftId) || [];
        if (found.length === 1) {
          this.socialPostFeedService.deleteDraft({ data: found[0] } as FeedItem);
          this.removeDrafts([draftId]);
        } else if (found?.length > 1) {
          // exclude the specific draft item from the group
          postsToEdit = posts.filter((p) => p.draftId === draftId && p.itemId !== itemId) || [];
        }
        return postsToEdit;
      }),
      switchMap((posts: SocialPost[]) => {
        if (posts.length > 0) {
          this.preparePostSubmission(posts);
          this.composer.submitDraft();
        }
        return EMPTY;
      }),
      catchError(() => {
        return EMPTY;
      }),
    );
  }

  preparePostSubmission(posts: SocialPost[], newDate?: Date): void {
    const services = new Map<PostableService, string>();
    const gmbOptions = new GmbOptions();

    posts.forEach((p) => {
      const postId = p.isDraft ? p.draftId : p.postId || p['socialPostId'];
      services.set({ ssid: p.ssid } as PostableService, postId);

      if (p.event) {
        gmbOptions.makeEvent = !!p.event.title;
        gmbOptions.eventOptions = {
          endDate: new Date(p.event.endDateTime),
          startDate: new Date(p.event.startDateTime),
          title: p.event.title,
        } as GmbEventOptions;
      }

      if (p.callToAction) {
        gmbOptions.addCta = !!p.callToAction.url || (!p.callToAction.url && p.callToAction.type === 'CALL');
        gmbOptions.ctaOptions = {
          ctaUrl: p.callToAction.url,
          action: p.callToAction.type,
        } as GmbCtaOptions;
      }
    });

    const uploadedMediaObjects: UploadedFile[] = [];
    let mediaEntries: MediaEntryInterface[] = [];

    const post = posts[0];
    const hasMediaEntries = post?.mediaEntries?.length > 0;
    const uniqueImageUrls = [...new Set(post?.imageUrls || [])];
    const videoUrl = post?.videoUrl || '';
    const isInstagramStory =
      Array.isArray(post.metaData) &&
      post.metaData.some((meta) => meta.propertyName === 'workflow_type' && meta.propertyValue === 'STORY_WORKFLOW');

    if (isInstagramStory && hasMediaEntries) {
      mediaEntries = post.mediaEntries
        .map(({ mediaUrl, mediaType }) => ({
          mediaUrl,
          mediaType: this.normalizeMediaType(mediaType),
        }))
        .filter(({ mediaType }) => ['MEDIA_TYPE_IMAGE', 'MEDIA_TYPE_VIDEO'].includes(mediaType))
        .slice(0, 1);

      uploadedMediaObjects.push(
        ...mediaEntries.map(({ mediaUrl, mediaType }) => ({
          url: mediaUrl,
          fileType: mediaType === 'MEDIA_TYPE_IMAGE' ? FileType.IMAGE : FileType.VIDEO,
          name: mediaType === 'MEDIA_TYPE_IMAGE' ? 'image.jpg' : 'video.mp4',
          size: 0,
        })),
      );

      post.imageUrls = (mediaEntries || [])
        .filter((entry) => entry.mediaType === 'MEDIA_TYPE_IMAGE')
        .map((entry) => entry.mediaUrl);
      post.videoUrl = mediaEntries.find((entry) => entry.mediaType === 'MEDIA_TYPE_VIDEO')?.mediaUrl || '';
    } else if (hasMediaEntries) {
      mediaEntries = post.mediaEntries
        .map(({ mediaUrl, mediaType }) => ({
          mediaUrl,
          mediaType: this.normalizeMediaType(mediaType),
        }))
        .filter(({ mediaType }) => ['MEDIA_TYPE_IMAGE', 'MEDIA_TYPE_VIDEO'].includes(mediaType));

      uploadedMediaObjects.push(
        ...mediaEntries.map(({ mediaUrl, mediaType }) => {
          const isImage = mediaType === 'MEDIA_TYPE_IMAGE';
          return {
            url: mediaUrl,
            fileType: isImage ? FileType.IMAGE : FileType.VIDEO,
            name: isImage ? 'image.jpg' : 'video.mp4',
            size: 0,
          };
        }),
      );

      post.imageUrls = [
        ...new Set(
          mediaEntries.filter((entry) => entry.mediaType === 'MEDIA_TYPE_IMAGE').map((entry) => entry.mediaUrl),
        ),
      ];
    } else if (uniqueImageUrls.length && videoUrl) {
      mediaEntries = [
        ...uniqueImageUrls.map((url) => ({ mediaUrl: url, mediaType: 'MEDIA_TYPE_IMAGE' })),
        { mediaUrl: videoUrl, mediaType: 'MEDIA_TYPE_VIDEO' },
      ];

      uploadedMediaObjects.push(
        ...uniqueImageUrls.map((url) => ({
          url,
          fileType: FileType.IMAGE,
          name: 'image.jpg',
          size: 0,
        })),
        {
          url: videoUrl,
          fileType: FileType.VIDEO,
          name: 'video.mp4',
          size: 0,
        },
      );

      post.imageUrls = uniqueImageUrls;
    } else if (uniqueImageUrls.length) {
      mediaEntries = uniqueImageUrls.map((url) => ({
        mediaUrl: url,
        mediaType: 'MEDIA_TYPE_IMAGE',
      }));

      uploadedMediaObjects.push(
        ...uniqueImageUrls.map((url) => ({
          url,
          fileType: FileType.IMAGE,
          name: 'image.jpg',
          size: 0,
        })),
      );

      post.imageUrls = uniqueImageUrls;
    } else if (videoUrl) {
      mediaEntries = [{ mediaUrl: videoUrl, mediaType: 'MEDIA_TYPE_VIDEO' }];

      uploadedMediaObjects.push({
        url: videoUrl,
        fileType: FileType.VIDEO,
        name: 'video.mp4',
        size: 0,
      });

      post.imageUrls = [];
    } else {
      post.imageUrls = [];
      post.videoUrl = null;
    }

    this.composer.initializeStore(this.config.accountGroupId);

    this.composer.applySettings({
      draftId: post.draftId,
      isHiddenDraft: post.isHidden,
      groupedCustomization: new Customization({
        services,
        postText: post.postText,
        scheduledDate: newDate || post.scheduledDateTime,
        uploadedMediaObjects,
        mediaEntries,
        gmbOptions,
      }),
      isEditing: true,
      workFlowType: this.getWorfklowTypeFromPost(post),
      postType: post.postType,
    });
  }

  normalizeMediaType(type: string): 'MEDIA_TYPE_IMAGE' | 'MEDIA_TYPE_VIDEO' | string {
    switch (type?.toUpperCase()) {
      case 'IMAGE':
        return 'MEDIA_TYPE_IMAGE';
      case 'VIDEO':
        return 'MEDIA_TYPE_VIDEO';
      default:
        return type;
    }
  }

  removePost(postId: string): void {
    const nextFeedItems = this.feedItems$$.getValue().filter((item) => item.postId !== postId);
    this.feedItems$$.next(nextFeedItems);
  }

  removePosts(postIds: string[]): void {
    const nextFeedItems = this.feedItems$$.getValue().filter((item) => !postIds.includes(item.postId));
    this.feedItems$$.next(nextFeedItems);
  }

  removeDrafts(draftIds: string[]): void {
    const nextDraftItems = this.feedItems$$.getValue().filter((item) => !draftIds.includes(item.draftId));
    this.feedItems$$.next(nextDraftItems);
  }

  deletePost(postId: string): Observable<any> {
    const url = `/account/${this.config.accountGroupId}/post/delete/`;
    const body = Payload.encodeURLComponent('post_id', postId);
    const options = { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } };
    return this.http.post(url, body, options);
  }

  //Calculate the keys for grouping in the calendar view.
  getEventCalendarGroupKey(event: CalendarEvent): string {
    let idx = '';

    if (event.meta?.isDraft) {
      idx = event.meta?.draftId;
    } else {
      //TASK MANAGER:
      //FacebookPost-SORD:0a352347aab3491d9a31e7d5f9aead2b-FBP-5BF836122D5A401D9659304D82D6BE33 - Simple task without subtasks
      //FacebookPost-SORD:0a352347aab3491d9a31e7d5f9aead2b:663581b6-FBP-5BF836122D5A401D9659304D82D6BE33 - Task with subtasks
      //SINGLE POST:
      //FacebookPost-0a352347aab3491d9a31e7d5f9aead2b-FBP-5BF836122D5A401D9659304D82D6BE33
      const postIdType = PostsCalendarService.getPostIdType(event.meta.postId);
      let groupId = event.meta.postId.split('-')[1]; //parentTaskId or singlePostId
      if (postIdType === PostIdType.SubTask) {
        groupId = groupId.slice(0, groupId.lastIndexOf(':')); //remove subtaskId to group them all in parentTaskId
      }

      idx = groupId + event.title + event.meta.images + event.meta.video + event.meta.errors;
    }

    return idx;
  }

  getWorfklowTypeFromPost(post: SocialPost): WorkflowType {
    const metaData = post?.metaData?.find((meta) => meta.propertyName === 'workflow_type');
    return metaData ? (metaData.propertyValue as WorkflowType) : WorkflowType.POST_WORKFLOW;
  }
}
