import { ChangeDetectionStrategy, Component, computed, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewEncapsulation } from '@angular/core';
import { combineLatest, firstValueFrom, Observable, of, Subject } from 'rxjs';
import { filter, map, shareReplay, startWith, switchMap } from 'rxjs/operators';
import { AccountGroup } from '../../account-group';
import { getLocation } from '../../account-group/account-group';
import { AppConfigService } from '../../app-config.service';
import { ImageService } from '../../core/image.service';
import { LocationsService } from '../../locations';
import { AccountGroupMetricService } from '../../metrics/account-group.service';
import { AdvertisingMetricsService } from '../../metrics/advertising.service';
import { GmbService } from '../../metrics/gmb.service';
import { ListingService } from '../../metrics/listing.service';
import { ReviewRequestsService } from '../../metrics/review-requests.service';
import { ReviewsService } from '../../metrics/reviews.service';
import { NavigationService } from '../../navigation/navigation.service';
import { Mode, SidepanelService, Size } from '../../navigation/sidepanel.service';
import { PageId } from '../../page-access';
import { PageAccessService } from '../../page-access/page-access.service';
import { SocialCardsService } from '../../reports/executive-report/report-section/cards/social-cards/social-cards.service';
import { CompareTab } from '../brand-compare/brand-compare.component';
import { BrandSidebarHeaderService } from '../brand-sidebar-header/brand-sidebar-header.service';
import { BrandRow } from '../table/table.service';
import { BrandDashboardSidebarComponent } from './brand-dashboard-sidebar/brand-dashboard-sidebar.component';
import { CustomerRelationsQueryService } from '../../performance-dashboard/connected-cards/customer-relations/customer-relations-query.service';
import { BingInsightsService } from '../../metrics/bing.service';
import { FeatureFlagService } from '@vendasta/businesses';
import { partnerId } from '../../../globals';

const listingLoadingTabs = [
  {
    titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.LISTINGS.LISTING_SCORE_TAB',
    measureKey: 'listing_score',
  },
  {
    titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.LISTINGS.ACCURACY_TAB',
    measureKey: 'accuracy',
  },
];

const loadingRepRequestTabs = [
  {
    titleTranslateKey: 'PERFORMANCE.REPUTATION.REQUESTS_DASHBOARD.SENT',
  },
  {
    titleTranslateKey: 'PERFORMANCE.REPUTATION.REQUESTS_DASHBOARD.CTR',
  },
  {
    titleTranslateKey: 'PERFORMANCE.REPUTATION.REQUESTS_DASHBOARD.CLICKED',
  },
];

const loadingReviewRequestTabs = [
  {
    titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.CUSTOMER_VOICE.REVIEW_REQUEST.SENT',
  },
  {
    titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.CUSTOMER_VOICE.REVIEW_REQUEST.OPENED',
  },
  {
    titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.CUSTOMER_VOICE.REVIEW_REQUEST.CLICKED',
  },
];

const percentScoreListingLoadingTabs = [
  {
    titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.LISTINGS.LISTING_SCORE_TAB',
    measureKey: 'listing_score',
    displayValueType: 'percentage',
  },
  {
    titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.LISTINGS.ACCURACY_TAB',
    measureKey: 'accuracy',
  },
];

const loadingReviewTabs = [
  {
    titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.REVIEWS.AVERAGE_REVIEW_RATING_TAB',
    measureKey: 'average_rating',
  },
  {
    titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.REVIEWS.TOTAL_REVIEWS_TAB',
    measureKey: 'total_reviews',
  },
];

const loadingGMBTabs = [
  {
    titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.GMB.TOTAL_VIEWS',
    measureKey: 'total_views',
  },
  {
    titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.GMB.TOTAL_ACTIONS',
    measureKey: 'total_actions',
  },
];

const loadingBingTabs = [
  {
    titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.BING_INSIGHTS.TOTAL_VIEWS',
    measureKey: 'total_views',
  },
  {
    titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.BING_INSIGHTS.TOTAL_ACTIONS',
    measureKey: 'total_actions',
  },
];

const loadingSocialTabs = [
  {
    titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.SOCIAL.ENGAGEMENT',
    measureKey: 'total_queries',
  },
  {
    titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.SOCIAL.ALL_REACH',
    measureKey: 'total_views',
  },
  {
    titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.SOCIAL.SHARES',
    measureKey: 'total_actions',
  },
];

const advertisingLoadingTabs = [
  {
    titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.ADVERTISING.IMPRESSIONS',
    measureKey: 'impressions',
  },
  {
    titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.ADVERTISING.CLICKS',
    measureKey: 'clicks',
  },
  {
    titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.ADVERTISING.CONVERSIONS',
    measureKey: 'conversion',
  },
];

const loadingLeadsTabs = [
  {
    titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.LEADS.CONVERSATIONS',
    measureKey: 'conversations',
  },
  {
    titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.LEADS.LEADS',
    measureKey: 'leads',
  },
];

@Component({
  selector: 'bc-brand-dashboard',
  templateUrl: './brand-dashboard.component.html',
  styleUrls: ['./brand-dashboard.component.scss'],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class BrandDashboardComponent implements OnInit, OnDestroy {
  public LISTINGS_COLOR = '#47B75D';
  public REPUTATION_COLOR = '#F7CB16';
  public REQUEST_COLOR = '#e7892e';
  public SOCIAL_COLOR = '#33ACE2';
  public WEBSITE_COLOR = '#7F509F';
  public ADVERTISING_COLOR = '#057EC1';
  public GMB_COLOR = '#4756BB'; // This is the dark color from their logo
  public SEO_COLOR = '#E78931';
  public BING_COLOR = '#008373'; // Microsoft Bing color

  reviewsTabs$: Observable<CompareTab[] | null>;
  listingsTabs$: Observable<CompareTab[] | null>;
  gmbTabs$: Observable<CompareTab[] | null>;
  bingTabs$: Observable<CompareTab[] | null>;
  advertisingTabs$: Observable<CompareTab[] | null>;
  reviewRequestTabs$: Observable<CompareTab[] | null>;
  socialTabs$: Observable<CompareTab[] | null>;
  repRequestsTab$: Observable<CompareTab[] | null>;

  tabCount$: Observable<number>;

  mapLocations$: Observable<BrandRow[]>;
  loadedMapLocations$: Observable<BrandRow[]>;

  private scoreAsPercent$: Observable<boolean>;

  brandName$ = this.locationsService.currentLocation$.pipe(map((brand) => brand.title));
  locationCount$ = this.accountGroupMetricService.filteredAccountGroupIdsForPath$.pipe(map((r) => r?.size || 0));

  sidepanelOpen$: Observable<boolean>;

  private ngUnsubscribe = new Subject<void>();

  protected readonly adintelPath = Promise.resolve('../advertising-overview');
  protected readonly bingPath = Promise.resolve('../bing-insights');
  protected readonly socialPath = firstValueFrom(
    this.pageVisibilityService.isPageAccessible$(PageId.multi_location_embedded_social),
  ).then((canAccess) => {
    return canAccess ? '../social' : '../social-overview';
  });
  protected readonly listingsPath = firstValueFrom(
    this.pageVisibilityService.isPageAccessible$(PageId.multi_location_embedded_listings),
  ).then((canAccess) => {
    return canAccess ? '../listings' : '../listings-overview';
  });
  protected readonly gmbPath = Promise.resolve('../analytics/google');
  protected readonly reviewsPath = firstValueFrom(
    this.pageVisibilityService.isPageAccessible$(PageId.brand_reputation),
  ).then((canAccess) => {
    return canAccess ? '../reputation' : '../reviews';
  });
  protected readonly requestsPath = firstValueFrom(
    this.pageVisibilityService.isPageAccessible$(PageId.brand_reputation),
  ).then((canAccess) => {
    return canAccess ? '../reputation' : '../requests';
  });

  protected readonly leadsTabs = computed<CompareTab[]>(() => {
    if (
      this.customerRelationsQueryService.leadsResource.isLoading() ||
      this.customerRelationsQueryService.conversationsCreatedResource.isLoading()
    ) {
      // null acts as placeholders so the shimmers show
      return loadingLeadsTabs;
    }

    const conversationsData = this.customerRelationsQueryService.conversationsCreatedResource.value();
    const conversationDelta = (conversationsData?.data?.total || 0) - (conversationsData?.data?.totalBefore || 0);
    const conversationTab = {
      titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.LEADS.CONVERSATIONS',
      value: conversationsData?.data?.total || 0,
      deltaAbs: conversationDelta,
      showDeltaAbs: true,
      deltaGood: conversationDelta > 0,
    };

    const leadsData = this.customerRelationsQueryService.leadsResource.value();
    const leadsDelta = (leadsData?.data?.total || 0) - (leadsData?.data?.totalBefore || 0);
    const leadsTab = {
      titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.LEADS.LEADS',
      value: leadsData?.data?.total || 0,
      deltaAbs: leadsDelta,
      showDeltaAbs: true,
      deltaGood: leadsDelta > 0,
    };

    return [conversationTab, leadsTab];
  });

  constructor(
    private nav: NavigationService,
    private reviewsService: ReviewsService,
    private listingService: ListingService,
    private gmbService: GmbService,
    private advertisingService: AdvertisingMetricsService,
    private pageVisibilityService: PageAccessService,
    private accountGroupService: AccountGroupMetricService,
    private sidebarHeaderService: BrandSidebarHeaderService,
    public sidepanelService: SidepanelService,
    private locationsService: LocationsService,
    private appConfigService: AppConfigService,
    public readonly imageService: ImageService,
    private reviewRequestsService: ReviewRequestsService,
    private socialCardsService: SocialCardsService,
    private accountGroupMetricService: AccountGroupMetricService,
    private customerRelationsQueryService: CustomerRelationsQueryService,
    private bingInsightsService: BingInsightsService,
    private featureFlagService: FeatureFlagService,
  ) {}

  ngOnInit(): void {
    this.sidepanelService.setView(Size.LARGE, Mode.SIDE, BrandDashboardSidebarComponent);

    this.sidepanelOpen$ = this.sidepanelService.visible$;

    this.scoreAsPercent$ = this.appConfigService.legacyConfig$.pipe(map((config) => config.showListingScoreAsPercent));

    const repCompareTabs$: Observable<CompareTab[]> = combineLatest([
      this.reviewsService.currentOverall$,
      this.reviewsService.previousOverall$,
    ]).pipe(
      map(([rating, pRating]) => {
        if (!rating) {
          return loadingReviewTabs;
        }
        let deltaRating, deltaTotal;
        if (pRating) {
          deltaRating = rating.averageReviewScore() - pRating.averageReviewScore();
          deltaTotal = rating.totalReviews() - pRating.totalReviews();
        }
        return [
          {
            titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.REVIEWS.AVERAGE_REVIEW_RATING_TAB',
            measureKey: 'average_rating',
            displayValueType: 'fraction',
            changeDisplayValueType: 'number',
            outOfValue: 5,
            value: rating.averageReviewScore(),
            deltaAbs: deltaRating,
            deltaRel: 0,
            deltaGood: deltaRating > 0,
            showDeltaAbs: true,
          },
          {
            titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.REVIEWS.REVIEWS_TAB',
            measureKey: 'total_reviews',
            value: rating.totalReviews(),
            deltaAbs: deltaTotal,
            deltaRel: 0,
            deltaGood: deltaTotal > 0,
            showDeltaAbs: true,
          },
        ];
      }),
      startWith(loadingReviewTabs),
    );

    const listingCompareTabs$: Observable<CompareTab[]> = combineLatest([
      this.listingService.overallListingScoreAndPresenceAndDeltas$,
      this.listingService.dashboardListingsAccuracy$,
      this.scoreAsPercent$,
    ]).pipe(
      map(([scoreAndPresenceBundle, accuracyData, scoreAsPercent]) => {
        if (
          [scoreAndPresenceBundle, accuracyData].some((x) => {
            return x == null || typeof x === 'undefined';
          })
        ) {
          if (scoreAsPercent) {
            return percentScoreListingLoadingTabs;
          }
          return listingLoadingTabs;
        }
        return [
          {
            titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.LISTINGS.LISTING_SCORE_TAB',
            measureKey: 'listing_score',
            displayValueType: scoreAsPercent ? 'percentage' : 'number',
            value: scoreAsPercent
              ? scoreAndPresenceBundle.filteredAvgScorePercent
              : scoreAndPresenceBundle.filteredAvgScore,
            deltaAbs: scoreAsPercent
              ? scoreAndPresenceBundle.filteredAvgScorePercentChange
              : scoreAndPresenceBundle.filteredAvgScoreChange,
            deltaRel: 0,
            deltaGood:
              (scoreAsPercent
                ? scoreAndPresenceBundle.filteredAvgScorePercentChange
                : scoreAndPresenceBundle.filteredAvgScoreChange) > 0,
            showDeltaAbs: true,
          },
          {
            titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.LISTINGS.ACCURACY_TAB',
            measureKey: 'accuracy',
            displayValueType: 'percentage',
            value: accuracyData.value,
            deltaAbs: accuracyData.delta,
            deltaRel: accuracyData.delta,
            deltaGood: accuracyData.delta > 0,
            showDeltaAbs: true,
            currentMeasure: accuracyData.delta === 0,
          },
        ];
      }),
      startWith(listingLoadingTabs),
    );

    const gmbCompareTabs$: Observable<CompareTab[]> = combineLatest([
      this.gmbService.currentOverallInsights$,
      this.gmbService.previousOverallInsights$,
      this.gmbService.currentOverallSearchKeywords$,
      this.gmbService.previousOverallSearchKeywords$,
    ]).pipe(
      map(([insights, pInsights, searchKeywords, pSearchKeywords]) => {
        if (!insights) {
          return loadingGMBTabs;
        }
        let deltaViews, deltaActions, deltaSearches;
        if (pInsights) {
          deltaViews = insights.totalViews() - pInsights.totalViews();
          deltaActions = insights.totalActions() - pInsights.totalActions();
          deltaSearches = searchKeywords - pSearchKeywords;
        }

        const tabs = [];
        tabs.push({
          titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.GMB.TOTAL_VIEWS',
          measureKey: 'total_views',
          value: insights.totalViews(),
          deltaAbs: deltaViews,
          deltaRel: 0,
          deltaGood: deltaViews > 0,
          showDeltaAbs: true,
        });
        tabs.push({
          titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.GMB.TOTAL_ACTIONS',
          measureKey: 'total_actions',
          value: insights.totalActions(),
          deltaAbs: deltaActions,
          deltaRel: 0,
          deltaGood: deltaActions > 0,
          showDeltaAbs: true,
        });
        tabs.push({
          titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.GMB.SEARCHES',
          measureKey: 'searches',
          value: searchKeywords,
          deltaAbs: deltaSearches,
          deltaRel: 0,
          deltaGood: deltaSearches > 0,
          showDeltaAbs: true,
        });
        return tabs;
      }),
      startWith(loadingGMBTabs),
    );

    const bingCompareTabs$: Observable<CompareTab[]> = combineLatest([
      this.bingInsightsService.currentBingViewCardInsights$,
      this.bingInsightsService.previousBingViewCardInsights$,
      this.bingInsightsService.currentBingActionsInsights$,
      this.bingInsightsService.previousBingActionsInsights$,
    ]).pipe(
      map(([currentViews, previousViews, currentActions, previousActions]) => {
        if (!currentViews || !currentActions) {
          return loadingBingTabs;
        }

        const deltaViews = previousViews ? currentViews.totalViews - previousViews.totalViews : null;

        const deltaActions = previousActions ? currentActions.totalActions - previousActions.totalActions : null;

        const tabs: CompareTab[] = [
          {
            titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.BING_INSIGHTS.TOTAL_VIEWS',
            measureKey: 'total_views',
            value: currentViews.totalViews,
            deltaAbs: deltaViews ?? 0,
            deltaRel: 0,
            deltaGood: (deltaViews ?? 0) > 0,
            showDeltaAbs: true,
          },
          {
            titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.BING_INSIGHTS.TOTAL_ACTIONS',
            measureKey: 'total_actions',
            value: currentActions.totalActions,
            deltaAbs: deltaActions ?? 0,
            deltaRel: 0,
            deltaGood: (deltaActions ?? 0) > 0,
            showDeltaAbs: true,
          },
        ];

        return tabs;
      }),
      startWith(loadingBingTabs),
    );

    const socialTabs$: Observable<CompareTab[]> = this.socialCardsService.dashboardData$.pipe(
      filter((tabs) => !!tabs),
      startWith(loadingSocialTabs),
    );

    const advertisingTabs$: Observable<CompareTab[]> = combineLatest([
      this.advertisingService.adIntelStatsOverall$,
      this.advertisingService.prevAdIntelStatsOverall$,
    ]).pipe(
      map(([overallStats, prevOverallStats]) => {
        if (!overallStats || !prevOverallStats) {
          return advertisingLoadingTabs;
        }
        const tabs: CompareTab[] = [
          {
            titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.ADVERTISING.IMPRESSIONS',
            measureKey: 'impressions',
            value: overallStats.impressions,
            deltaAbs: overallStats.impressions - prevOverallStats.impressions,
            deltaRel: 0,
            deltaGood: overallStats.impressions - prevOverallStats.impressions > 0,
            showDeltaAbs: true,
          },
          {
            titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.ADVERTISING.CLICKS',
            measureKey: 'clicks',
            value: overallStats.clicks,
            deltaAbs: overallStats.clicks - prevOverallStats.clicks,
            deltaRel: 0,
            deltaGood: overallStats.clicks - prevOverallStats.clicks > 0,
            showDeltaAbs: true,
          },
          {
            titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.ADVERTISING.CONVERSIONS',
            measureKey: 'conversions',
            value: overallStats.conversions,
            deltaAbs: overallStats.conversions - prevOverallStats.conversions,
            deltaRel: 0,
            deltaGood: overallStats.conversions - prevOverallStats.conversions > 0,
            showDeltaAbs: true,
          },
        ];
        return tabs;
      }),
      startWith(advertisingLoadingTabs),
    );

    const reviewRequestCompareTabs$: Observable<CompareTab[]> = combineLatest([
      this.reviewRequestsService.currentReviewRequestStats$,
      this.reviewRequestsService.previousReviewRequestStats$,
    ]).pipe(
      map(([currentStats, previousStats]) => {
        if (!currentStats || !previousStats) {
          return loadingReviewRequestTabs;
        }
        let deltaSent: number, deltaOpened: number, deltaClicked: number;
        if (previousStats) {
          deltaSent = currentStats.sent - previousStats.sent;
          deltaOpened = currentStats.opened - previousStats.opened;
          deltaClicked = currentStats.clicked - previousStats.clicked;
        }
        return [
          {
            titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.CUSTOMER_VOICE.REVIEW_REQUEST.SENT',
            value: currentStats.sent,
            deltaAbs: deltaSent,
            deltaRel: 0,
            deltaGood: deltaSent > 0,
            showDeltaAbs: true,
          },
          {
            titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.CUSTOMER_VOICE.REVIEW_REQUEST.OPENED',
            displayValueType: 'percentage',
            changeDisplayValueType: 'percentage',
            value: currentStats.opened,
            deltaAbs: deltaOpened,
            deltaRel: 0,
            deltaGood: deltaOpened > 0,
            showDeltaAbs: true,
          },
          {
            titleTranslateKey: 'PERFORMANCE.MULTI_LOCATION.CUSTOMER_VOICE.REVIEW_REQUEST.CLICKED',
            displayValueType: 'percentage',
            changeDisplayValueType: 'percentage',
            value: currentStats.clicked,
            deltaAbs: deltaClicked,
            deltaRel: 0,
            deltaGood: deltaClicked > 0,
            showDeltaAbs: true,
          },
        ];
      }),
      startWith(loadingReviewRequestTabs),
    );

    const repRequestsCompareTabs$: Observable<CompareTab[]> = combineLatest([
      this.reviewRequestsService.currentRepReviewRequestStats$,
      this.reviewRequestsService.previousRepReviewRequestStats$,
    ]).pipe(
      map(([currentStats, previousStats]) => {
        if (!currentStats || !previousStats) {
          return loadingRepRequestTabs;
        }
        let deltaSent: number, deltaCTR: number, deltaClicked: number;
        if (previousStats) {
          deltaSent = currentStats.requestSent - previousStats.requestSent;
          deltaCTR = currentStats.ctr - previousStats.ctr;
          deltaClicked = currentStats.requestClicked - previousStats.requestClicked;
        }
        return [
          {
            titleTranslateKey: 'PERFORMANCE.REPUTATION.REQUESTS_DASHBOARD.SENT',
            value: currentStats.requestSent,
            deltaAbs: deltaSent,
            deltaRel: 0,
            deltaGood: deltaSent > 0,
            showDeltaAbs: true,
          },
          {
            titleTranslateKey: 'PERFORMANCE.REPUTATION.REQUESTS_DASHBOARD.CTR',
            displayValueType: 'percentage',
            changeDisplayValueType: 'percentage',
            value: currentStats.ctr,
            deltaAbs: deltaCTR,
            deltaRel: 0,
            deltaGood: deltaCTR > 0,
            showDeltaAbs: true,
          },
          {
            titleTranslateKey: 'PERFORMANCE.REPUTATION.REQUESTS_DASHBOARD.CLICKED',
            value: currentStats.requestClicked,
            deltaAbs: deltaClicked,
            deltaRel: 0,
            deltaGood: deltaClicked > 0,
            showDeltaAbs: true,
          },
        ];
      }),
      startWith(loadingRepRequestTabs),
    );

    // Switch tab access observables into highlight card data
    this.reviewsTabs$ = this.pageVisibilityService
      .isPageAccessible$(PageId.multi_location_reviews)
      .pipe(switchMap((isVisible) => (isVisible ? repCompareTabs$ : of(null))));
    this.reviewRequestTabs$ = this.pageVisibilityService
      .isPageAccessible$(PageId.request_multi_location_reviews)
      .pipe(switchMap((isVisible) => (isVisible ? reviewRequestCompareTabs$ : of(null))));
    this.repRequestsTab$ = this.pageVisibilityService
      .isPageAccessible$(PageId.multi_location_requests_overview)
      .pipe(switchMap((isVisible) => (isVisible ? repRequestsCompareTabs$ : of(null))));
    this.listingsTabs$ = this.pageVisibilityService
      .isPageAccessible$(PageId.multi_location_listings)
      .pipe(switchMap((isVisible) => (isVisible ? listingCompareTabs$ : of(null))));
    this.gmbTabs$ = this.pageVisibilityService
      .isPageAccessible$(PageId.multi_location_google_my_business)
      .pipe(switchMap((isVisible) => (isVisible ? gmbCompareTabs$ : of(null))));
    this.bingTabs$ = combineLatest([
      this.pageVisibilityService.isPageAccessible$(PageId.multi_location_google_my_business), // Temporary until correct PageId is available
      this.featureFlagService.checkFeatureFlag(partnerId, '', 'bing_insights_multi_location_busines_app'),
    ]).pipe(
      switchMap(([isVisible, isFeatureEnabled]) => {
        if (isVisible && isFeatureEnabled) {
          return bingCompareTabs$;
        }
        return of(null);
      }),
    );
    this.socialTabs$ = this.pageVisibilityService
      .isPageAccessible$(PageId.multi_location_social)
      .pipe(switchMap((isVisible) => (isVisible ? socialTabs$ : of(null))));
    this.advertisingTabs$ = this.pageVisibilityService
      .isPageAccessible$(PageId.multi_location_advertising)
      .pipe(switchMap((isVisible) => (isVisible ? advertisingTabs$ : of(null))));
    // TODO: masking of advertising card by config

    this.tabCount$ = combineLatest([
      this.pageVisibilityService.isPageAccessible$(PageId.multi_location_reviews),
      this.pageVisibilityService.isPageAccessible$(PageId.request_multi_location_reviews),
      this.pageVisibilityService.isPageAccessible$(PageId.multi_location_listings),
      this.pageVisibilityService.isPageAccessible$(PageId.multi_location_google_my_business),
      this.pageVisibilityService.isPageAccessible$(PageId.multi_location_social),
      this.pageVisibilityService.isPageAccessible$(PageId.multi_location_advertising),
    ]).pipe(
      map((visibilities) => {
        return visibilities.reduce((count, vis) => {
          return count + (vis ? 1 : 0);
        }, 1); // The leads card is always shown, if we allow leads to be hidden this should be set to 0
      }),
    );

    this.mapLocations$ = this.accountGroupService.filteredLocationsForPath$.pipe(
      map((locations) => {
        if (locations == null) {
          return null;
        }
        return Object.keys(locations)
          .map((k) => locations[k])
          .map((ag: AccountGroup) => {
            return {
              accountGroup: ag,
              title: ag.companyName,
              subtitle: getLocation(ag),
              grade: 'None',
            };
          });
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    this.loadedMapLocations$ = this.mapLocations$.pipe(filter((locs) => !!locs));
  }

  ngOnDestroy(): void {
    this.sidepanelService.clearView();
    this.sidepanelService.close();
    this.nav.setActionButtons([]);
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }

  openDetailsDrawer(accountGroupId: string): void {
    this.sidebarHeaderService.setResource(accountGroupId);
    this.sidepanelService.open();
  }
}
