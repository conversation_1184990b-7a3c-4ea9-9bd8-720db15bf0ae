import { Component, OnInit } from '@angular/core';
import { combineLatest } from 'rxjs';
import { map } from 'rxjs/operators';
import { ImageService } from '../../../../../core/image.service';
import { LocationsService, isBrand } from '../../../../../locations';
import {
  CardConfig,
  CardDataContainer,
  MultiLocationCardSourceConfig,
} from '../../../../../performance-dashboard/cards/interface';
import { ExecutiveReportQueryService, ReportType } from '../../../executive-report-query.service';
import {
  ActionsMetricsConfig,
  SearchKeywordsMetricsConfig,
  ViewsMetricsConfig,
} from './google-my-business-cards.config';
import { DateBoundedSearchData, GoogleMyBusinessCardsService } from './google-my-business-cards.service';

@Component({
  selector: 'bc-google-my-business-cards',
  templateUrl: './google-my-business-cards.component.html',
  styleUrls: ['./google-my-business-cards.component.scss'],
  standalone: false,
})
export class GoogleMyBusinessCardsComponent implements OnInit {
  viewsMetricsConfig: CardConfig = ViewsMetricsConfig;
  viewsMetricsData: CardDataContainer;
  actionsMetricsConfig: CardConfig = ActionsMetricsConfig;
  actionsMetricsData: CardDataContainer;
  searchesMetricsConfig: CardConfig = SearchKeywordsMetricsConfig;
  searchesMetricsData: CardDataContainer;
  insightsData$: any;
  searchKeywordData$: any;

  constructor(
    private googleMyBusinessCardsService: GoogleMyBusinessCardsService,
    private executiveReportQueryService: ExecutiveReportQueryService,
    private imageService: ImageService,
    private locationsService: LocationsService,
  ) {}

  ngOnInit(): void {
    this.insightsData$ = combineLatest([
      this.googleMyBusinessCardsService.getCurrentAndPreviousInsights(),
      this.googleMyBusinessCardsService.getHistoricalInsights(),
      this.googleMyBusinessCardsService.getDataLocationCount(),
      this.executiveReportQueryService.typeOfReport$,
      this.locationsService.currentLocation$,
    ]).pipe(
      map(([currentData, allTimeData, count, reportType, brand]) => {
        const footerConfig: MultiLocationCardSourceConfig = {
          locationCount: count,
          linkTextKey: 'BRANDS.GOOGLE_MY_BUSINESS.REPORT.LINK_TITLE',
          sourceIconUrl: this.imageService.getImageSrc('google-logo.svg'),
        };
        // If it is a multi-location report use the multi-location footer
        if (reportType === ReportType.MULTI_LOCATION && isBrand(brand)) {
          footerConfig.linkUrl = `/account/brands/${brand.groupNodes[0]}/analytics/google`;
        }

        this.viewsMetricsConfig.headingConfig.sourceConfig = footerConfig;
        this.actionsMetricsConfig.headingConfig.sourceConfig = footerConfig;

        // Create new objects here as Angular or the NGX-Charting library struggles with
        // change detection inconsistently
        currentData.views.chartData = Object.assign([], allTimeData.views);
        this.viewsMetricsData = Object.assign([], currentData.views);
        currentData.actions.chartData = Object.assign([], allTimeData.actions);
        this.actionsMetricsData = Object.assign([], currentData.actions);
      }),
    );

    this.searchKeywordData$ = combineLatest([
      this.googleMyBusinessCardsService.getCurrentAndPreviousSearchKeywords(),
      this.googleMyBusinessCardsService.getHistoricalSearchKeywords(),
      this.googleMyBusinessCardsService.getSearchDataLocationCount(),
      this.executiveReportQueryService.typeOfReport$,
      this.locationsService.currentLocation$,
    ]).pipe(
      map(([currentSearches, allTimeSearches, count, reportType, brand]) => {
        const footerConfig: MultiLocationCardSourceConfig = {
          locationCount: count,
          linkTextKey: 'BRANDS.GOOGLE_MY_BUSINESS.REPORT.LINK_TITLE',
          sourceIconUrl: this.imageService.getImageSrc('google-logo.svg'),
        };
        // If it is a multi-location report use the multi-location footer
        if (reportType === ReportType.MULTI_LOCATION && isBrand(brand)) {
          footerConfig.linkUrl = `/account/brands/${brand.groupNodes[0]}/analytics/google`;
        }

        this.searchesMetricsConfig.headingConfig.sourceConfig = footerConfig;

        currentSearches.searches.chartData = Object.assign([], allTimeSearches.searches);

        if (
          currentSearches?.searches?.chartData?.[0]['data'] &&
          currentSearches.searches.chartData[0]['data'].length === 1 &&
          allTimeSearches?.['start'] &&
          allTimeSearches?.['end']
        ) {
          adjustSearchData(currentSearches, allTimeSearches['start'], allTimeSearches['end']);
        }
        this.searchesMetricsData = Object.assign([], currentSearches.searches);
      }),
    );
  }
}

// Adjusts search data by distributing a single value across multiple data points using start and end dates.
function adjustSearchData(currentSearches: DateBoundedSearchData, start: string, end: string): DateBoundedSearchData {
  // get the value and split it in half
  const value = currentSearches.searches.chartData[0]['data'][0].value;
  const convertedValue = Math.round(value / 2);

  // delete the first and the only element in the array
  currentSearches.searches.chartData[0]['data'].shift();

  // construct the new start and end objects
  const startDate = {
    name: start,
    value: convertedValue,
  };
  const endDate = {
    name: end,
    value: convertedValue,
  };

  // add the new start and end objects to the array
  currentSearches.searches.chartData[0]['data'].unshift(startDate);
  currentSearches.searches.chartData[0]['data'].unshift(endDate);

  return currentSearches;
}
